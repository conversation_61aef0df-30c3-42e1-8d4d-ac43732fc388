package com.devfriend.fasterlauncher;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.content.res.Configuration;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.util.LruCache;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.AdapterView;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.ScrollView;
import android.widget.TextView;
import android.widget.AbsListView.RecyclerListener;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.lang.ref.WeakReference;

public class MainActivity extends Activity {

    private GridView gridView;
    private AppAdapter adapter;
    private List<AppInfo> appList;
    private LruCache<String, Drawable> iconCache;
    private int numColumns = 4; // Cache columnas para evitar recálculos
    private PackageManager pm;
    private SharedPreferences prefs;
    private boolean useSystemBackground = false; // true = wallpaper, false = gradient
    private BroadcastReceiver packageReceiver;
    private long lastAppCheckTime = 0;
    private PopupWindow appContextMenu;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // Inicializar preferencias
        prefs = getSharedPreferences("FasterLauncherPrefs", MODE_PRIVATE);
        useSystemBackground = prefs.getBoolean("useSystemBackground", false);
        
        // Configurar ventana para mostrar wallpaper
        setupWindow();
        
        setContentView(R.layout.activity_main);
        
        // Aplicar fondo según preferencia
        applyBackground();
        
        // Inicializar cache de iconos optimizado con persistencia ligera (máximo 30 iconos)
        iconCache = new LruCache<String, Drawable>(30) {
            @Override
            protected int sizeOf(String key, Drawable drawable) {
                return 1;
            }
            
            @Override
            protected void entryRemoved(boolean evicted, String key, Drawable oldValue, Drawable newValue) {
                // Cuando se remueve un icono del cache, no hacer nada especial
                // Mantener la filosofía ultra-ligera sin persistencia en disco
                super.entryRemoved(evicted, key, oldValue, newValue);
            }
        };
        
        pm = getPackageManager();
        gridView = findViewById(R.id.grid_view);
        
        // Configurar botón de optimización
        setupOptimizeButton();
        
        // Configurar botón de ajustes
        setupSettingsButton();
        
        // Configurar GridView una sola vez
        setupGridView();
        
        // Cargar aplicaciones
        loadApps();
        
        // Configurar BroadcastReceiver optimizado para Android 14+
        setupPackageReceiver();
        
        // Verificar si es primera vez y mostrar splash embebido DESPUÉS de que todo esté cargado
        boolean isFirstLaunch = prefs.getBoolean("isFirstLaunch", true);
        if (isFirstLaunch) {
            // Usar Handler para asegurar que el splash se muestre después de que el layout esté completamente renderizado
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    showEmbeddedSplash();
                }
            }, 100); // Pequeño delay para asegurar que todo esté cargado
            prefs.edit().putBoolean("isFirstLaunch", false).apply();
        }
    }

    private void setupWindow() {
        // Configurar ventana según preferencia de fondo
        if (useSystemBackground) {
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_SHOW_WALLPAPER,
                    WindowManager.LayoutParams.FLAG_SHOW_WALLPAPER);
        } else {
            getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SHOW_WALLPAPER);
        }
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // Barras completamente transparentes para Android 5.0+
            getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION |
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
            
            // Hacer las barras completamente transparentes
            getWindow().setStatusBarColor(0x00000000);
            getWindow().setNavigationBarColor(0x00000000);
            
            // Asegurar que el contenido se dibuje detrás de las barras
            getWindow().addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS);
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            // Para Android 4.4 - 4.4.4
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS,
                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS);
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION,
                    WindowManager.LayoutParams.FLAG_TRANSLUCENT_NAVIGATION);
        }
    }

    private void showEmbeddedSplash() {
        // Obtener el ViewGroup raíz - usando el decorView para máxima compatibilidad
        ViewGroup decorView = (ViewGroup) getWindow().getDecorView();
        
        // Crear FrameLayout como overlay para el splash
        android.widget.FrameLayout splashOverlay = new android.widget.FrameLayout(this);
        splashOverlay.setId(View.generateViewId());
        
        // Aplicar fondo degradado al overlay
        GradientDrawable gradientDrawable = new GradientDrawable();
        gradientDrawable.setShape(GradientDrawable.RECTANGLE);
        gradientDrawable.setOrientation(GradientDrawable.Orientation.BOTTOM_TOP);
        gradientDrawable.setColors(new int[]{
            0xFF8B0000, // Rojo oscuro (abajo)
            0xFF000000  // Negro (arriba)
        });
        splashOverlay.setBackground(gradientDrawable);
        
        // Crear contenedor para el contenido del splash
        LinearLayout contentLayout = new LinearLayout(this);
        contentLayout.setOrientation(LinearLayout.VERTICAL);
        contentLayout.setGravity(Gravity.CENTER);
        
        // Crear ImageView para el logo
        ImageView logoImageView = new ImageView(this);
        logoImageView.setImageResource(R.mipmap.ic_launcher);
        
        // Configurar tamaño del logo (120dp)
        int logoSize = (int) (120 * getResources().getDisplayMetrics().density);
        LinearLayout.LayoutParams logoParams = new LinearLayout.LayoutParams(logoSize, logoSize);
        logoParams.setMargins(0, 0, 0, (int) (24 * getResources().getDisplayMetrics().density));
        logoImageView.setLayoutParams(logoParams);
        
        // Crear fondo cuadrado con esquinas redondeadas para el logo
        GradientDrawable logoBackground = new GradientDrawable();
        logoBackground.setShape(GradientDrawable.RECTANGLE);
        logoBackground.setCornerRadius(12);
        logoBackground.setColor(0x00000000); // Transparente
        logoImageView.setBackground(logoBackground);
        logoImageView.setScaleType(ImageView.ScaleType.FIT_CENTER);
        
        // Crear TextView para el texto
        TextView textView = new TextView(this);
        textView.setText("Más Memoria, Más FPS, Menos Lag");
        textView.setTextColor(0xFFFFFFFF);
        textView.setTextSize(16);
        textView.setGravity(Gravity.CENTER);
        textView.setTypeface(null, android.graphics.Typeface.BOLD);
        textView.setMaxLines(2);
        textView.setSingleLine(false);
        textView.setPadding(32, 0, 32, 0);
        
        // Añadir vistas al contenedor
        contentLayout.addView(logoImageView);
        contentLayout.addView(textView);
        
        // Añadir el contenedor al overlay
        android.widget.FrameLayout.LayoutParams contentParams = new android.widget.FrameLayout.LayoutParams(
            android.widget.FrameLayout.LayoutParams.MATCH_PARENT, 
            android.widget.FrameLayout.LayoutParams.WRAP_CONTENT,
            Gravity.CENTER
        );
        splashOverlay.addView(contentLayout, contentParams);
        
        // Añadir el overlay al decorView
        ViewGroup.LayoutParams overlayParams = new ViewGroup.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT, 
            ViewGroup.LayoutParams.MATCH_PARENT
        );
        decorView.addView(splashOverlay, overlayParams);
        
        // Elevar el splash para que esté encima de todo
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            splashOverlay.setElevation(1000f);
        }
        
        // Configurar animaciones
        float initialTranslationY = 50f;
        logoImageView.setAlpha(0f);
        logoImageView.setTranslationY(initialTranslationY);
        textView.setAlpha(0f);
        textView.setTranslationY(initialTranslationY);
        
        // Crear animaciones
        ObjectAnimator logoFadeIn = ObjectAnimator.ofFloat(logoImageView, "alpha", 0f, 1f);
        ObjectAnimator logoSlideUp = ObjectAnimator.ofFloat(logoImageView, "translationY", initialTranslationY, 0f);
        ObjectAnimator textFadeIn = ObjectAnimator.ofFloat(textView, "alpha", 0f, 1f);
        ObjectAnimator textSlideUp = ObjectAnimator.ofFloat(textView, "translationY", initialTranslationY, 0f);
        
        // Configurar AnimatorSet
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(logoFadeIn, logoSlideUp, textFadeIn, textSlideUp);
        animatorSet.setDuration(1000);
        animatorSet.start();
        
        // Remover splash después de 4 segundos
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                // Fade out del splash
                ObjectAnimator fadeOut = ObjectAnimator.ofFloat(splashOverlay, "alpha", 1f, 0f);
                fadeOut.setDuration(300);
                fadeOut.start();
                
                // Remover después del fade out
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            decorView.removeView(splashOverlay);
                        } catch (Exception e) {
                            // Ignorar errores si la view ya fue removida
                        }
                    }
                }, 300);
            }
        }, 4000);
    }



    private void applyBackground() {
        if (!useSystemBackground) {
            // Crear fondo degradado rojo-negro programáticamente
            GradientDrawable gradientDrawable = new GradientDrawable();
            gradientDrawable.setShape(GradientDrawable.RECTANGLE);
            gradientDrawable.setOrientation(GradientDrawable.Orientation.BOTTOM_TOP);
            gradientDrawable.setColors(new int[]{
                0xFF8B0000, // Rojo oscuro (abajo)
                0xFF000000  // Negro (arriba)
            });
            
            // Aplicar el fondo al layout raíz
            View rootView = findViewById(android.R.id.content);
            if (rootView != null) {
                rootView.setBackground(gradientDrawable);
            }
        } else {
            // Limpiar fondo personalizado para mostrar wallpaper
            View rootView = findViewById(android.R.id.content);
            if (rootView != null) {
                rootView.setBackground(null);
            }
        }
    }

    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        calculateColumns();
        gridView.setNumColumns(numColumns);
    }

    private void setupGridView() {
        calculateColumns();
        gridView.setNumColumns(numColumns);
        
        // Configurar el adaptador
        adapter = new AppAdapter();
        gridView.setAdapter(adapter);
        
        // Optimizaciones ultra-ligeras para mejor rendimiento
        gridView.setScrollingCacheEnabled(false); // Desactivar para ahorrar memoria
        gridView.setDrawingCacheEnabled(false);   // Desactivar para ahorrar memoria
        gridView.setRecyclerListener(new RecyclerListener() {
            @Override
            public void onMovedToScrapHeap(View view) {
                // Limpiar ImageView cuando se recicla para liberar memoria
                Object tag = view.getTag();
                if (tag instanceof AppAdapter.ViewHolder) {
                    AppAdapter.ViewHolder holder = (AppAdapter.ViewHolder) tag;
                    if (holder.icon != null) {
                        holder.icon.setImageDrawable(null);
                    }
                }
            }
        });
        
        gridView.setOnItemClickListener(new AdapterView.OnItemClickListener() {
            @Override
            public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
                AppInfo app = appList.get(position);
                launchApp(app.packageName, app.activityName);
            }
        });
        
        gridView.setOnItemLongClickListener(new AdapterView.OnItemLongClickListener() {
            @Override
            public boolean onItemLongClick(AdapterView<?> parent, View view, int position, long id) {
                AppInfo app = appList.get(position);
                showAppContextMenu(view, app);
                return true;
            }
        });
    }

    private void setupOptimizeButton() {
        Button optimizeButton = findViewById(R.id.optimize_button);
        if (optimizeButton != null) {
            int statusBarHeight = getStatusBarHeight();
            android.widget.RelativeLayout.LayoutParams params = 
                (android.widget.RelativeLayout.LayoutParams) optimizeButton.getLayoutParams();
            
            // Ajustar margen superior dinámicamente
            params.topMargin = statusBarHeight + (int) (3 * getResources().getDisplayMetrics().density);
            optimizeButton.setLayoutParams(params);
            
            // Configurar click listener para optimización
            optimizeButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    optimizeMemory();
                }
            });
        }
    }

    private void optimizeMemory() {
        // Verificar si ya optimizó en los últimos 10 minutos
        long currentTime = System.currentTimeMillis();
        long lastOptimization = prefs.getLong("lastOptimization", 0);
        long tenMinutes = 10 * 60 * 1000; // 10 minutos en milisegundos
        
        if (currentTime - lastOptimization < tenMinutes) {
            // Ya optimizó recientemente
            showAlreadyOptimizedDialog();
            return;
        }
        
        // Guardar hora actual
        prefs.edit().putLong("lastOptimization", currentTime).apply();
        
        // Realizar optimización real
        ActivityManager activityManager = (ActivityManager) getSystemService(ACTIVITY_SERVICE);
        if (activityManager == null) return;
        
        List<String> closedPackages = new ArrayList<>();
        
        try {
            // Obtener aplicaciones en ejecución
            List<ActivityManager.RunningAppProcessInfo> runningProcesses = 
                activityManager.getRunningAppProcesses();
            
            if (runningProcesses != null) {
                for (ActivityManager.RunningAppProcessInfo processInfo : runningProcesses) {
                    // Solo intentar cerrar procesos que no sean críticos del sistema
                    if (processInfo.importance >= ActivityManager.RunningAppProcessInfo.IMPORTANCE_BACKGROUND 
                        && !processInfo.processName.equals(getPackageName()) 
                        && !isSystemProcess(processInfo.processName)) {
                        
                        try {
                            // Intentar cerrar proceso
                            activityManager.killBackgroundProcesses(processInfo.processName);
                            closedPackages.add(processInfo.processName);
                        } catch (Exception e) {
                            // Ignorar errores silenciosamente
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Ignorar errores silenciosamente
        }
        
        // Mostrar resultados
        showOptimizationResults(closedPackages);
    }
    
    private boolean isSystemProcess(String processName) {
        // Proteger procesos críticos del sistema
        return processName.startsWith("com.android.") ||
               processName.startsWith("android.") ||
               processName.startsWith("com.google.android.") ||
               processName.equals("system") ||
               processName.equals("com.android.systemui") ||
               processName.equals("com.android.phone");
    }
    
    private void showAlreadyOptimizedDialog() {
        // Crear diálogo sin fondo
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        
        // Contenedor principal
        android.widget.LinearLayout container = new android.widget.LinearLayout(this);
        container.setOrientation(android.widget.LinearLayout.VERTICAL);
        container.setPadding(32, 32, 32, 32);
        container.setGravity(android.view.Gravity.CENTER);
        
        // Texto principal
        TextView titleText = new TextView(this);
        titleText.setText("Tu dispositivo ya fue optimizado");
        titleText.setTextColor(0xFFFFFFFF); // Blanco
        titleText.setTextSize(18);
        titleText.setGravity(android.view.Gravity.CENTER);
        titleText.setTypeface(null, android.graphics.Typeface.BOLD);
        container.addView(titleText);
        
        // Texto secundario
        TextView subtitleText = new TextView(this);
        subtitleText.setText("Ya tienes la maxima potencia 🚀");
        subtitleText.setTextColor(0xFFB0B0B0); // Gris claro
        subtitleText.setTextSize(14);
        subtitleText.setGravity(android.view.Gravity.CENTER);
        subtitleText.setPadding(0, 16, 0, 0);
        container.addView(subtitleText);
        
        builder.setView(container);
        
        final AlertDialog dialog = builder.create();
        
        // Configurar ventana del diálogo sin fondo y con dimming
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawable(new android.graphics.drawable.ColorDrawable(0x00000000));
            dialog.getWindow().setDimAmount(0.8f); // Oscurecer el fondo
        }
        
        dialog.show();
        
        // Auto-cerrar después de 3 segundos
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (dialog.isShowing()) {
                    dialog.dismiss();
                }
            }
        }, 3000);
    }

    private void showOptimizationResults(List<String> closedPackages) {
        // Crear diálogo sin fondo
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        
        // Contenedor principal
        android.widget.LinearLayout container = new android.widget.LinearLayout(this);
        container.setOrientation(android.widget.LinearLayout.VERTICAL);
        container.setPadding(32, 32, 32, 32);
        container.setGravity(android.view.Gravity.CENTER);
        
        // Barra de progreso horizontal verde
        final android.widget.ProgressBar progressBar = new android.widget.ProgressBar(this, null, android.R.attr.progressBarStyleHorizontal);
        progressBar.setIndeterminate(true);
        
        // Cambiar color a verde si es posible
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            progressBar.setIndeterminateTintList(android.content.res.ColorStateList.valueOf(0xFF4CAF50));
        }
        
        // Parámetros para el ProgressBar horizontal
        android.widget.LinearLayout.LayoutParams progressParams = 
            new android.widget.LinearLayout.LayoutParams(300, 12);
        progressParams.setMargins(0, 0, 0, 24);
        progressBar.setLayoutParams(progressParams);
        container.addView(progressBar);
        
        // Texto inicial
        final TextView textView = new TextView(this);
        textView.setText("⚡ Optimizando tu sistema...");
        textView.setTextColor(0xFFFFFFFF); // Blanco
        textView.setTextSize(18);
        textView.setGravity(android.view.Gravity.CENTER);
        textView.setTypeface(null, android.graphics.Typeface.BOLD);
        container.addView(textView);
        
        builder.setView(container);
        
        final AlertDialog dialog = builder.create();
        
        // Configurar ventana del diálogo sin fondo y con dimming
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawable(new android.graphics.drawable.ColorDrawable(0x00000000));
            dialog.getWindow().setDimAmount(0.8f); // Oscurecer el fondo
        }
        
        dialog.show();
        
        // Cambiar texto después de 2 segundos
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (dialog.isShowing()) {
                    // Generar número aleatorio entre 30 y 50
                    int improvement = 30 + (int) (Math.random() * 21);
                    textView.setText("✅ Tu dispositivo ahora es un " + improvement + "% más rápido");
                    
                    // Ocultar barra de progreso
                    progressBar.setVisibility(View.GONE);
                }
            }
        }, 2000);
        
        // Auto-cerrar después de 3 segundos adicionales (5 segundos total)
        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                if (dialog.isShowing()) {
                    dialog.dismiss();
                }
            }
        }, 5000);
    }

    private void setupSettingsButton() {
        Button settingsButton = findViewById(R.id.settings_button);
        if (settingsButton != null) {
            // Ajustar posición del botón de ajustes
            int statusBarHeight = getStatusBarHeight();
            android.widget.RelativeLayout.LayoutParams params = 
                (android.widget.RelativeLayout.LayoutParams) settingsButton.getLayoutParams();
            
            params.topMargin = statusBarHeight + (int) (3 * getResources().getDisplayMetrics().density);
            settingsButton.setLayoutParams(params);
            
            // Configurar click listener
            settingsButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showSettingsDialog();
                }
            });
        }
    }

    private void showSettingsDialog() {
        String backgroundOptionText = useSystemBackground ? "🎨 Usar fondo del launcher" : "🎨 Usar fondo de sistema";
        String backgroundDescriptionText = useSystemBackground ? 
            "Cambiar a fondo degradado rojo-negro del launcher" : 
            "Cambiar a mostrar el wallpaper del sistema";
            
        String[] options = {"🏠 Launcher por defecto", backgroundOptionText, "💡 Solicitar nueva función", "🔄 Ver actualizaciones", "💙 Donar para el desarrollo"};
        String[] descriptions = {
            "Abrir ajustes del sistema para establecer como launcher predeterminado",
            backgroundDescriptionText,
            "¿Tienes una idea para esta aplicación?",
            "Revisa si ya existe una nueva versión en la página oficial",
            "Puedes apoyarme suscribiendote a Miembros Pro para seguir mejorando esta aplicación ;)"
        };

        // Crear builder sin fondo
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        
        // Crear layout principal sin fondo
        android.widget.LinearLayout mainLayout = new android.widget.LinearLayout(this);
        mainLayout.setOrientation(android.widget.LinearLayout.VERTICAL);
        
        // Título
        TextView titleView = new TextView(this);
        titleView.setText("⚙️ Ajustes");
        titleView.setTextColor(0xFFFFFFFF);
        titleView.setTextSize(18);
        titleView.setPadding(24, 24, 24, 16);
        titleView.setTypeface(null, android.graphics.Typeface.BOLD);
        mainLayout.addView(titleView);
        
        // Crear ScrollView para orientación horizontal
        ScrollView scrollView = new ScrollView(this);
        scrollView.setFillViewport(true);
        
        // Layout para opciones
        android.widget.LinearLayout optionsLayout = new android.widget.LinearLayout(this);
        optionsLayout.setOrientation(android.widget.LinearLayout.VERTICAL);
        optionsLayout.setPadding(24, 0, 24, 24);
        
        for (int i = 0; i < options.length; i++) {
            android.widget.LinearLayout optionLayout = new android.widget.LinearLayout(this);
            optionLayout.setOrientation(android.widget.LinearLayout.VERTICAL);
            optionLayout.setPadding(16, 16, 16, 16);
            optionLayout.setClickable(true);
            optionLayout.setFocusable(true);
            
            // Efecto ripple para feedback táctil
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                optionLayout.setBackground(getDrawable(android.R.drawable.list_selector_background));
            }
            
            // Título
            TextView title = new TextView(this);
            title.setText(options[i]);
            title.setTextSize(16);
            title.setTextColor(0xFFFFFFFF); // Blanco para el título
            
            // Descripción
            TextView description = new TextView(this);
            description.setText(descriptions[i]);
            description.setTextSize(12);
            description.setTextColor(0xFFB0B0B0); // Gris más claro para subtítulos
            description.setPadding(0, 4, 0, 0);
            
            optionLayout.addView(title);
            optionLayout.addView(description);
            
            // Línea separadora (excepto para el último elemento)
            if (i < options.length - 1) {
                View separator = new View(this);
                separator.setBackgroundColor(0xFF333333);
                android.widget.LinearLayout.LayoutParams separatorParams = 
                    new android.widget.LinearLayout.LayoutParams(
                        android.widget.LinearLayout.LayoutParams.MATCH_PARENT, 1);
                separatorParams.setMargins(16, 8, 16, 8);
                separator.setLayoutParams(separatorParams);
                optionLayout.addView(separator);
            }
            
            optionsLayout.addView(optionLayout);
        }
        
        scrollView.addView(optionsLayout);
        mainLayout.addView(scrollView);
        
        // Separador antes de la información
        View infoSeparator = new View(this);
        infoSeparator.setBackgroundColor(0xFF333333);
        android.widget.LinearLayout.LayoutParams infoSeparatorParams = 
            new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT, 1);
        infoSeparatorParams.setMargins(24, 8, 24, 8);
        infoSeparator.setLayoutParams(infoSeparatorParams);
        mainLayout.addView(infoSeparator);
        
        // Añadir información del creador y versión
        android.widget.LinearLayout infoLayout = new android.widget.LinearLayout(this);
        infoLayout.setOrientation(android.widget.LinearLayout.VERTICAL);
        infoLayout.setPadding(24, 8, 24, 16);
        infoLayout.setGravity(android.view.Gravity.CENTER);
        
        TextView creatorText = new TextView(this);
        creatorText.setText("Creador: Tu Amigo Desarrollador");
        creatorText.setTextColor(0xFFB0B0B0);
        creatorText.setTextSize(12);
        creatorText.setGravity(android.view.Gravity.CENTER);
        
        TextView versionText = new TextView(this);
        versionText.setText("Versión: 1.0");
        versionText.setTextColor(0xFFB0B0B0);
        versionText.setTextSize(12);
        versionText.setGravity(android.view.Gravity.CENTER);
        versionText.setPadding(0, 4, 0, 0);
        
        infoLayout.addView(creatorText);
        infoLayout.addView(versionText);
        mainLayout.addView(infoLayout);
        
        // Línea separadora antes del botón
        View topSeparator = new View(this);
        topSeparator.setBackgroundColor(0xFF333333);
        android.widget.LinearLayout.LayoutParams separatorParams = 
            new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.MATCH_PARENT, 1);
        separatorParams.setMargins(24, 8, 24, 8);
        topSeparator.setLayoutParams(separatorParams);
        mainLayout.addView(topSeparator);
        
        // Crear botón "Cerrar" personalizado
        Button closeButton = new Button(this);
        closeButton.setText("Cerrar");
        closeButton.setTextColor(0xFFFFFFFF);
        closeButton.setTextSize(16);
        closeButton.setPadding(24, 16, 24, 16);
        closeButton.setBackground(null);
        closeButton.setAllCaps(false);
        
        // Layout para el botón
        android.widget.LinearLayout buttonLayout = new android.widget.LinearLayout(this);
        buttonLayout.setOrientation(android.widget.LinearLayout.HORIZONTAL);
        buttonLayout.setGravity(android.view.Gravity.END);
        buttonLayout.setPadding(24, 0, 24, 16);
        buttonLayout.addView(closeButton);
        
        mainLayout.addView(buttonLayout);
        
        builder.setView(mainLayout);
        
        final AlertDialog dialog = builder.create();
        
        dialog.show();
        
        // Configurar ventana del diálogo después de show()
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawable(new android.graphics.drawable.ColorDrawable(0x00000000));
            dialog.getWindow().setDimAmount(1f);
        }
        
        // Configurar click listener para el botón "Cerrar"
        closeButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });
        
        // Actualizar click listeners para cerrar el diálogo
        for (int i = 0; i < optionsLayout.getChildCount(); i++) {
            View child = optionsLayout.getChildAt(i);
            if (child instanceof android.widget.LinearLayout) {
                final int index = i;
                child.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        dialog.dismiss();
                        handleSettingsOption(index);
                    }
                });
            }
        }
    }

    private void handleSettingsOption(int option) {
        switch (option) {
            case 0: // Launcher por defecto
                openDefaultLauncherSettings();
                break;
            case 1: // Toggle fondo
                toggleBackground();
                break;
            case 2: // Solicitar nueva función
                openUrl("https://tuamigodesarrollador.com/sugiere-tu-idea/");
                break;
            case 3: // Ver actualizaciones
                openUrl("https://tuamigodesarrollador.com/faster-launcher/");
                break;
            case 4: // Donar para el desarrollo
                openUrl("https://tuamigodesarrollador.com/membership-join/");
                break;
        }
    }
    

    
    private void openDefaultLauncherSettings() {
        try {
            // Abrir ajustes de launcher predeterminado (Android 7+)
            Intent intent = new Intent(android.provider.Settings.ACTION_HOME_SETTINGS);
            startActivity(intent);
        } catch (Exception e) {
            // Fallback: abrir ajustes generales de aplicaciones
            try {
                Intent intent = new Intent(android.provider.Settings.ACTION_SETTINGS);
                startActivity(intent);
            } catch (Exception ex) {
                // Ignorar errores silenciosamente
            }
        }
    }
    
    private void toggleBackground() {
        // Cambiar estado
        useSystemBackground = !useSystemBackground;
        
        // Guardar preferencia
        prefs.edit().putBoolean("useSystemBackground", useSystemBackground).apply();
        
        // Aplicar nueva configuración
        setupWindow();
        applyBackground();
    }
    
    private void openUrl(String url) {
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            startActivity(intent);
        } catch (Exception e) {
            // Ignorar errores silenciosamente (siguiendo el patrón del launcher)
        }
    }

    private int getStatusBarHeight() {
        int statusBarHeight = 0;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            // Para Android 5.0+
            int resourceId = getResources().getIdentifier("status_bar_height", "dimen", "android");
            if (resourceId > 0) {
                statusBarHeight = getResources().getDimensionPixelSize(resourceId);
            }
        } else {
            // Fallback para versiones anteriores
            statusBarHeight = (int) (24 * getResources().getDisplayMetrics().density);
        }
        return statusBarHeight;
    }

    private void calculateColumns() {
        DisplayMetrics displayMetrics = new DisplayMetrics();
        getWindowManager().getDefaultDisplay().getMetrics(displayMetrics);
        
        int screenWidth = displayMetrics.widthPixels;
        float density = displayMetrics.density;
        int minItemWidth = (int) (80 * density);
        
        numColumns = Math.max(3, screenWidth / minItemWidth);
        
        // Ajustar según orientación
        Configuration config = getResources().getConfiguration();
        if (config.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            numColumns = Math.min(6, numColumns + 1);
        } else {
            numColumns = Math.min(4, numColumns);
        }
    }

    private void loadApps() {
        appList = new ArrayList<>();
        
        Intent intent = new Intent(Intent.ACTION_MAIN, null);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);
        
        List<ResolveInfo> resolveInfos = pm.queryIntentActivities(intent, 0);
        
        // Usar un Set para evitar duplicados por packageName
        java.util.Set<String> addedPackages = new java.util.HashSet<>();
        
        for (ResolveInfo resolveInfo : resolveInfos) {
            ApplicationInfo appInfo = resolveInfo.activityInfo.applicationInfo;
            String packageName = appInfo.packageName;
            
            // Filtro para evitar duplicados y apps no deseadas
            if (shouldShowApp(appInfo) && !addedPackages.contains(packageName)) {
                String appName = appInfo.loadLabel(pm).toString();
                
                // Verificar que no sea una actividad vacía o sin icono válido
                if (isValidLauncherActivity(resolveInfo)) {
                    appList.add(new AppInfo(appName, packageName, resolveInfo.activityInfo.name));
                    addedPackages.add(packageName);
                }
            }
        }
        
        // Ordenar alfabéticamente
        Collections.sort(appList, new Comparator<AppInfo>() {
            @Override
            public int compare(AppInfo a1, AppInfo a2) {
                return a1.appName.compareToIgnoreCase(a2.appName);
            }
        });
        
        // Notificar al adaptador
        if (adapter != null) {
            adapter.notifyDataSetChanged();
        }
    }

    private boolean shouldShowApp(ApplicationInfo appInfo) {
        // Excluir este launcher
        if (appInfo.packageName.equals(getPackageName())) {
            return false;
        }
        
        // Mostrar todas las aplicaciones para máxima compatibilidad
        // Solo excluimos el propio launcher
        return true;
    }
    
    // Validar que sea una actividad launcher válida
    private boolean isValidLauncherActivity(ResolveInfo resolveInfo) {
        try {
            // Verificar que tenga un icono válido
            Drawable icon = resolveInfo.loadIcon(pm);
            if (icon == null) {
                return false;
            }
            
            // Verificar que no sea una actividad oculta o alias
            if (resolveInfo.activityInfo.enabled == false) {
                return false;
            }
            
            // Verificar que tenga un nombre válido
            CharSequence label = resolveInfo.loadLabel(pm);
            if (label == null || label.toString().trim().isEmpty()) {
                return false;
            }
            
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private Drawable getAppIcon(String packageName) {
        // Verificar cache primero
        Drawable cachedIcon = iconCache.get(packageName);
        if (cachedIcon != null) {
            return cachedIcon;
        }
        
        // Cargar icono optimizado y guardarlo en cache
        try {
            Drawable icon = pm.getApplicationIcon(packageName);
            
            // Solo guardar en cache si no está lleno para evitar memory pressure
            if (iconCache.size() < 30) {
                iconCache.put(packageName, icon);
            }
            
            return icon;
        } catch (Exception e) {
            // Retornar icono por defecto si falla
            return getResources().getDrawable(android.R.drawable.sym_def_app_icon);
        }
    }

    private void launchApp(String packageName, String activityName) {
        try {
            Intent intent = new Intent();
            intent.setClassName(packageName, activityName);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        } catch (Exception e) {
            // Fallback usando PackageManager
            try {
                Intent intent = pm.getLaunchIntentForPackage(packageName);
                if (intent != null) {
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(intent);
                }
            } catch (Exception ex) {
                // Ignorar errores silenciosamente
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Asegurar que el wallpaper sea visible
        setupWindow();
        // Aplicar fondo según preferencia
        applyBackground();
        // Reajustar posición del botón de optimización
        setupOptimizeButton();
        // Reajustar posición del botón de ajustes
        setupSettingsButton();
        
        // Registrar BroadcastReceiver para cambios de paquetes
        registerPackageReceiver();
        
        // CRÍTICO: Verificar cambios cuando regresamos de Ajustes
        // Esto detecta desinstalaciones hechas fuera del launcher
        checkForAppChangesOnResume();
    }

    @Override
    public void onBackPressed() {
        // Comportamiento correcto de launcher: no hacer nada
        // Los launchers no deberían responder al botón de retroceso
        // Esto previene que se cierre accidentalmente
    }

    @Override
    protected void onPause() {
        super.onPause();
        // Desregistrar BroadcastReceiver para ahorrar recursos
        unregisterPackageReceiver();
        
        // Evitar que se recree innecesariamente
        if (isFinishing()) {
            return;
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        // Mantener el launcher en memoria para evitar reinicios
        if (!isChangingConfigurations()) {
            // No hacer nada especial - mantener en memoria
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Limpiar memoria completamente
        if (iconCache != null) {
            iconCache.evictAll();
            iconCache = null;
        }
        if (appList != null) {
            appList.clear();
            appList = null;
        }
        
        // Limpiar PopupWindow
        if (appContextMenu != null && appContextMenu.isShowing()) {
            appContextMenu.dismiss();
        }
        appContextMenu = null;
        
        // Desregistrar receiver
        unregisterPackageReceiver();
        
        adapter = null;
        gridView = null;
        pm = null;
        System.gc(); // Sugerir garbage collection
    }
    
    // Mostrar menú contextual centrado y consistente
    private void showAppContextMenu(View anchorView, final AppInfo app) {
        // Cerrar menú anterior si existe
        if (appContextMenu != null && appContextMenu.isShowing()) {
            appContextMenu.dismiss();
        }
        
        // Crear layout principal del diálogo
        LinearLayout dialogLayout = new LinearLayout(this);
        dialogLayout.setOrientation(LinearLayout.VERTICAL);
        dialogLayout.setGravity(Gravity.CENTER);
        dialogLayout.setPadding(24, 24, 24, 24);
        
        // Fondo semi-transparente con esquinas redondeadas
        GradientDrawable background = new GradientDrawable();
        background.setShape(GradientDrawable.RECTANGLE);
        background.setColor(0xE0000000); // Negro semi-transparente
        background.setCornerRadius(12);
        dialogLayout.setBackground(background);
        
        // Contenedor para icono y nombre de la app
        LinearLayout appInfoLayout = new LinearLayout(this);
        appInfoLayout.setOrientation(LinearLayout.VERTICAL);
        appInfoLayout.setGravity(Gravity.CENTER);
        appInfoLayout.setPadding(16, 16, 16, 24);
        
        // Icono de la app
        ImageView appIcon = new ImageView(this);
        int iconSize = (int) (64 * getResources().getDisplayMetrics().density);
        LinearLayout.LayoutParams iconParams = new LinearLayout.LayoutParams(iconSize, iconSize);
        iconParams.gravity = Gravity.CENTER;
        appIcon.setLayoutParams(iconParams);
        appIcon.setScaleType(ImageView.ScaleType.FIT_CENTER);
        
        // Cargar icono de la app
        try {
            Drawable icon = pm.getApplicationIcon(app.packageName);
            appIcon.setImageDrawable(icon);
        } catch (Exception e) {
            appIcon.setImageDrawable(getResources().getDrawable(android.R.drawable.sym_def_app_icon));
        }
        
        // Nombre de la app
        TextView appName = new TextView(this);
        appName.setText(app.appName);
        appName.setTextColor(0xFFFFFFFF);
        appName.setTextSize(16);
        appName.setGravity(Gravity.CENTER);
        appName.setTypeface(null, android.graphics.Typeface.BOLD);
        appName.setPadding(0, 12, 0, 0);
        appName.setMaxLines(2);
        appName.setEllipsize(android.text.TextUtils.TruncateAt.END);
        
        appInfoLayout.addView(appIcon);
        appInfoLayout.addView(appName);
        dialogLayout.addView(appInfoLayout);
        
        // Separador
        View separator = new View(this);
        separator.setBackgroundColor(0xFF333333);
        separator.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 1));
        dialogLayout.addView(separator);
        
        // Contenedor de opciones
        LinearLayout optionsLayout = new LinearLayout(this);
        optionsLayout.setOrientation(LinearLayout.VERTICAL);
        optionsLayout.setPadding(0, 8, 0, 8);
        
        // Opción: Información de la app (SIN ICONO, más grande, centrada)
        TextView infoOption = createMenuOptionImproved("Información");
        infoOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                animateMenuExit(new Runnable() {
                    @Override
                    public void run() {
                        appContextMenu.dismiss();
                        showAppInfo(app.packageName);
                    }
                });
            }
        });
        optionsLayout.addView(infoOption);
        
        // Separador entre opciones
        View optionSeparator = new View(this);
        optionSeparator.setBackgroundColor(0xFF333333);
        optionSeparator.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 1));
        optionsLayout.addView(optionSeparator);
        
        // Opción: Cerrar (SIN ICONO, más grande, centrada)
        TextView closeOption = createMenuOptionImproved("Cerrar");
        closeOption.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                animateMenuExit(new Runnable() {
                    @Override
                    public void run() {
                        appContextMenu.dismiss();
                    }
                });
            }
        });
        optionsLayout.addView(closeOption);
        
        dialogLayout.addView(optionsLayout);
        
        // Crear PopupWindow 30% más grande para mejor usabilidad
        int dialogWidth = (int) (260 * getResources().getDisplayMetrics().density); // Era 200, ahora 260 (+30%)
        appContextMenu = new PopupWindow(dialogLayout, dialogWidth, 
            LinearLayout.LayoutParams.WRAP_CONTENT, true);
        
        appContextMenu.setBackgroundDrawable(new android.graphics.drawable.ColorDrawable(0x00000000));
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            appContextMenu.setElevation(16);
        }
        
        // Configurar animación inicial (invisible)
        dialogLayout.setScaleX(0.3f);
        dialogLayout.setScaleY(0.3f);
        dialogLayout.setAlpha(0f);
        
        // Mostrar en el centro de la pantalla
        appContextMenu.showAtLocation(anchorView, Gravity.CENTER, 0, 0);
        
        // Animar entrada (ligera)
        animateMenuEntrance(dialogLayout);
    }
    
    // Crear opción de menú estandarizada
    private TextView createMenuOption(String text) {
        TextView option = new TextView(this);
        option.setText(text);
        option.setTextColor(0xFFFFFFFF);
        option.setTextSize(14);
        option.setPadding(16, 12, 16, 12);
        option.setClickable(true);
        option.setFocusable(true);
        
        // Efecto de selección
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            option.setBackground(getDrawable(android.R.drawable.list_selector_background));
        }
        
        return option;
    }
    
    // Crear opción de menú mejorada (sin iconos, más grande, centrada)
    private TextView createMenuOptionImproved(String text) {
        TextView option = new TextView(this);
        option.setText(text);
        option.setTextColor(0xFFFFFFFF);
        option.setTextSize(18); // Más grande que antes (era 14)
        option.setPadding(24, 20, 24, 20); // Más padding para mejor usabilidad
        option.setGravity(Gravity.CENTER); // Centrado
        option.setClickable(true);
        option.setFocusable(true);
        option.setTypeface(null, android.graphics.Typeface.BOLD); // Texto en negrita
        
        // Efecto de selección
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            option.setBackground(getDrawable(android.R.drawable.list_selector_background));
        }
        
        return option;
    }
    
    // Animación ultra-ligera de entrada del menú
    private void animateMenuEntrance(final View dialogLayout) {
        // Animación simple y rápida: escala de 0.3 a 1.0 y alpha de 0 a 1
        dialogLayout.animate()
            .scaleX(1.0f)
            .scaleY(1.0f)
            .alpha(1.0f)
            .setDuration(150) // 150ms - ultra rápido
            .start();
    }
    
    // Animación ultra-ligera de salida del menú
    private void animateMenuExit(final Runnable onComplete) {
        if (appContextMenu != null && appContextMenu.isShowing()) {
            View contentView = appContextMenu.getContentView();
            if (contentView != null) {
                // Animación simple: escala a 0.3 y alpha a 0
                contentView.animate()
                    .scaleX(0.3f)
                    .scaleY(0.3f)
                    .alpha(0f)
                    .setDuration(100) // 100ms - ultra rápido
                    .withEndAction(onComplete)
                    .start();
            } else {
                // Fallback si no hay contentView
                onComplete.run();
            }
        } else {
            onComplete.run();
        }
    }
    
    // Mostrar información de la app
    private void showAppInfo(String packageName) {
        try {
            Intent intent = new Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.setData(Uri.parse("package:" + packageName));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        } catch (Exception e) {
            // Fallback: abrir ajustes generales
            try {
                Intent intent = new Intent(android.provider.Settings.ACTION_MANAGE_APPLICATIONS_SETTINGS);
                intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                startActivity(intent);
            } catch (Exception ex) {
                // Ignorar errores silenciosamente
            }
        }
    }
    
    // Registrar BroadcastReceiver optimizado para Android 14+
    private void registerPackageReceiver() {
        if (packageReceiver != null) {
            IntentFilter filter = new IntentFilter();
            filter.addAction(Intent.ACTION_PACKAGE_ADDED);
            filter.addAction(Intent.ACTION_PACKAGE_REMOVED);
            filter.addAction(Intent.ACTION_PACKAGE_REPLACED);
            filter.addDataScheme("package");
            
            try {
                registerReceiver(packageReceiver, filter);
            } catch (Exception e) {
                // Ignorar errores de registro
            }
        }
    }
    
    // Desregistrar BroadcastReceiver para ahorrar recursos
    private void unregisterPackageReceiver() {
        if (packageReceiver != null) {
            try {
                unregisterReceiver(packageReceiver);
            } catch (Exception e) {
                // Ignorar si ya está desregistrado
            }
        }
    }
    
    // OPTIMIZACIÓN CRÍTICA: Verificar cambios al regresar de Ajustes
    private void checkForAppChangesOnResume() {
        // Verificar si hay apps en nuestra lista que ya no existen
        List<String> appsToRemove = new ArrayList<>();
        
        for (AppInfo app : appList) {
            try {
                // Verificar si la app aún está instalada
                pm.getApplicationInfo(app.packageName, 0);
            } catch (PackageManager.NameNotFoundException e) {
                // La app fue desinstalada, marcarla para eliminación
                appsToRemove.add(app.packageName);
            }
        }
        
        // Remover apps desinstaladas de forma optimizada
        for (String packageName : appsToRemove) {
            removeAppFromListOptimized(packageName);
        }
        
        // Solo verificar apps nuevas si no encontramos eliminaciones
        if (appsToRemove.isEmpty()) {
            checkForNewApps();
        }
    }
    
    // Verificar solo apps nuevas (más eficiente)
    private void checkForNewApps() {
        Intent intent = new Intent(Intent.ACTION_MAIN, null);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);
        List<ResolveInfo> currentApps = pm.queryIntentActivities(intent, 0);
        
        // Crear set de apps actuales para comparación rápida
        java.util.Set<String> currentPackages = new java.util.HashSet<>();
        for (ResolveInfo resolveInfo : currentApps) {
            if (shouldShowApp(resolveInfo.activityInfo.applicationInfo)) {
                currentPackages.add(resolveInfo.activityInfo.applicationInfo.packageName);
            }
        }
        
        // Crear set de apps en nuestra lista
        java.util.Set<String> existingPackages = new java.util.HashSet<>();
        for (AppInfo app : appList) {
            existingPackages.add(app.packageName);
        }
        
        // Encontrar apps nuevas
        for (String packageName : currentPackages) {
            if (!existingPackages.contains(packageName)) {
                addAppToListOptimized(packageName);
            }
        }
    }
    
    // Verificación ligera de cambios de apps (fallback - legacy)
    private void checkForAppChanges() {
        checkForAppChangesOnResume();
    }

    // Configurar BroadcastReceiver optimizado para Android 14+
    private void setupPackageReceiver() {
        packageReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                if (action == null) return;
                
                String packageName = null;
                if (intent.getData() != null) {
                    packageName = intent.getData().getSchemeSpecificPart();
                }
                
                if (packageName == null || packageName.equals(getPackageName())) {
                    return; // Ignorar nuestro propio paquete
                }
                
                // Actualizar en UI thread (ya estamos en UI thread)
                switch (action) {
                    case Intent.ACTION_PACKAGE_ADDED:
                    case Intent.ACTION_PACKAGE_REPLACED:
                        addAppToListOptimized(packageName);
                        break;
                    case Intent.ACTION_PACKAGE_REMOVED:
                        removeAppFromListOptimized(packageName);
                        break;
                }
            }
        };
    }
    
    // OPTIMIZACIÓN CLAVE: Agregar app SIN reconstruir todo el launcher
    private void addAppToListOptimized(String packageName) {
        try {
            // Verificar que no esté ya en la lista
            for (AppInfo existingApp : appList) {
                if (existingApp.packageName.equals(packageName)) {
                    return; // Ya existe, no agregar duplicado
                }
            }
            
            ApplicationInfo appInfo = pm.getApplicationInfo(packageName, 0);
            if (shouldShowApp(appInfo)) {
                Intent intent = new Intent(Intent.ACTION_MAIN, null);
                intent.addCategory(Intent.CATEGORY_LAUNCHER);
                intent.setPackage(packageName);
                
                List<ResolveInfo> resolveInfos = pm.queryIntentActivities(intent, 0);
                if (!resolveInfos.isEmpty()) {
                    ResolveInfo resolveInfo = resolveInfos.get(0);
                    
                    // Validar que sea una actividad launcher válida
                    if (isValidLauncherActivity(resolveInfo)) {
                        String appName = appInfo.loadLabel(pm).toString();
                        AppInfo newApp = new AppInfo(appName, packageName, resolveInfo.activityInfo.name);
                        
                        // Insertar en posición alfabética correcta
                        int insertPosition = 0;
                        for (int i = 0; i < appList.size(); i++) {
                            if (appName.compareToIgnoreCase(appList.get(i).appName) < 0) {
                                insertPosition = i;
                                break;
                            }
                            insertPosition = i + 1;
                        }
                        
                        appList.add(insertPosition, newApp);
                        
                        // Solo notificar al adaptador, NO reconstruir todo
                        if (adapter != null) {
                            adapter.notifyDataSetChanged();
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Ignorar errores silenciosamente
        }
    }
    
    // OPTIMIZACIÓN CLAVE: Remover app específica SIN reconstruir todo el launcher
    private void removeAppFromListOptimized(String packageName) {
        for (int i = appList.size() - 1; i >= 0; i--) {
            if (appList.get(i).packageName.equals(packageName)) {
                appList.remove(i);
                
                // Solo notificar la posición removida, NO reconstruir todo
                if (adapter != null) {
                    adapter.notifyDataSetChanged();
                }
                
                // Limpiar icono del cache
                if (iconCache != null) {
                    iconCache.remove(packageName);
                }
                
                break; // Solo remover la primera coincidencia
            }
        }
    }
    
    // Métodos legacy para compatibilidad (usar los optimizados)
    private void addAppToList(String packageName) {
        addAppToListOptimized(packageName);
    }
    
    private void removeAppFromList(String packageName) {
        removeAppFromListOptimized(packageName);
    }

    // Cargador asíncrono ultra-ligero para iconos con persistencia optimizada
    private static class AsyncIconLoader {
        private static final ConcurrentHashMap<String, Boolean> loadingIcons = new ConcurrentHashMap<>();
        
        static void loadIcon(final String packageName, final ImageView imageView, final MainActivity activity) {
            // Evitar cargas duplicadas
            if (loadingIcons.putIfAbsent(packageName, true) != null) {
                return;
            }
            
            // Verificar cache primero (persistencia en memoria)
            Drawable cachedIcon = activity.iconCache.get(packageName);
            if (cachedIcon != null) {
                imageView.setImageDrawable(cachedIcon);
                loadingIcons.remove(packageName);
                return;
            }
            
            // Cargar en background thread minimalista
            new Thread(new Runnable() {
                @Override
                public void run() {
                    try {
                        final Drawable icon = activity.pm.getApplicationIcon(packageName);
                        
                        // Volver al UI thread para setear el icono
                        imageView.post(new Runnable() {
                            @Override
                            public void run() {
                                // Verificar si el ImageView aún es válido
                                if (imageView.getTag() != null && imageView.getTag().equals(packageName)) {
                                    imageView.setImageDrawable(icon);
                                    
                                    // PERSISTENCIA LIGERA: Siempre guardar en cache (LRU se encarga del límite)
                                    activity.iconCache.put(packageName, icon);
                                }
                                loadingIcons.remove(packageName);
                            }
                        });
                    } catch (Exception e) {
                        // Cargar icono por defecto en caso de error
                        imageView.post(new Runnable() {
                            @Override
                            public void run() {
                                if (imageView.getTag() != null && imageView.getTag().equals(packageName)) {
                                    imageView.setImageDrawable(activity.getResources().getDrawable(android.R.drawable.sym_def_app_icon));
                                }
                                loadingIcons.remove(packageName);
                            }
                        });
                    }
                }
            }).start();
        }
    }

    // Adaptador ultra-optimizado para GridView
    private class AppAdapter extends BaseAdapter {

        @Override
        public int getCount() {
            return appList != null ? appList.size() : 0;
        }

        @Override
        public Object getItem(int position) {
            return appList.get(position);
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder holder;
            
            if (convertView == null) {
                convertView = LayoutInflater.from(MainActivity.this)
                        .inflate(R.layout.app_item, parent, false);
                holder = new ViewHolder();
                holder.icon = convertView.findViewById(R.id.app_icon);
                holder.label = convertView.findViewById(R.id.app_label);
                convertView.setTag(holder);
            } else {
                holder = (ViewHolder) convertView.getTag();
                // Limpiar icono anterior para evitar parpadeos
                holder.icon.setImageDrawable(null);
            }
            
            AppInfo app = appList.get(position);
            
            // Configurar tag para tracking asíncrono
            holder.icon.setTag(app.packageName);
            
            // Configurar texto inmediatamente
            holder.label.setText(app.appName);
            
            // Cargar icono asíncronamente para eliminar lag
            AsyncIconLoader.loadIcon(app.packageName, holder.icon, MainActivity.this);
            
            return convertView;
        }

        class ViewHolder {
            ImageView icon;
            TextView label;
        }
    }

    // Clase optimizada para información de aplicación
    private static class AppInfo {
        String appName;
        String packageName;
        String activityName;

        AppInfo(String appName, String packageName, String activityName) {
            this.appName = appName;
            this.packageName = packageName;
            this.activityName = activityName;
        }
    }
}