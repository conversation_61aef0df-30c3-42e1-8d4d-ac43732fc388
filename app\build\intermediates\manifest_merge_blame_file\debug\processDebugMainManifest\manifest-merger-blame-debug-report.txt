1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.devfriend.fasterlauncher"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
11-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:4:5-78
11-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:4:22-75
12    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />
12-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:5:5-84
12-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:5:22-81
13
14    <!-- Permisos para Android 14+ compatibilidad -->
15    <uses-permission
15-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:8:5-9:38
16        android:name="android.permission.QUERY_ALL_PACKAGES"
16-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:8:22-74
17        android:maxSdkVersion="29" />
17-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:9:9-35
18    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
18-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:10:5-81
18-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:10:22-78
19
20    <!-- Queries para Android 11+ para poder ver todas las aplicaciones -->
21    <queries>
21-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:13:5-18:15
22        <intent>
22-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:14:9-17:18
23            <action android:name="android.intent.action.MAIN" />
23-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:15:13-65
23-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:15:21-62
24
25            <category android:name="android.intent.category.LAUNCHER" />
25-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:16:13-73
25-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:16:23-70
26        </intent>
27    </queries>
28
29    <application
29-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:20:5-48:19
30        android:allowBackup="false"
30-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:21:9-36
31        android:debuggable="true"
32        android:extractNativeLibs="false"
33        android:icon="@mipmap/ic_launcher"
33-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:22:9-43
34        android:label="@string/app_name"
34-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:23:9-41
35        android:testOnly="true"
36        android:theme="@style/Theme.FasterLauncher" >
36-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:24:9-52
37        <activity
37-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:25:9-45:20
38            android:name="com.devfriend.fasterlauncher.MainActivity"
38-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:26:13-41
39            android:allowTaskReparenting="false"
39-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:38:13-49
40            android:alwaysRetainTaskState="true"
40-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:35:13-49
41            android:clearTaskOnLaunch="false"
41-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:31:13-46
42            android:configChanges="orientation|screenSize|keyboardHidden"
42-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:29:13-74
43            android:excludeFromRecents="true"
43-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:33:13-46
44            android:exported="true"
44-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:27:13-36
45            android:finishOnTaskLaunch="false"
45-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:34:13-47
46            android:launchMode="singleTask"
46-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:30:13-44
47            android:noHistory="false"
47-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:36:13-38
48            android:screenOrientation="unspecified"
48-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:28:13-52
49            android:stateNotNeeded="true"
49-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:32:13-42
50            android:taskAffinity="" >
50-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:37:13-36
51            <intent-filter android:priority="1" >
51-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:39:13-44:29
51-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:39:28-48
52                <action android:name="android.intent.action.MAIN" />
52-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:15:13-65
52-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:15:21-62
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:16:13-73
54-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:16:23-70
55                <category android:name="android.intent.category.HOME" />
55-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:42:17-73
55-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:42:27-70
56                <category android:name="android.intent.category.DEFAULT" />
56-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:43:17-76
56-->C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:43:27-73
57            </intent-filter>
58        </activity>
59    </application>
60
61</manifest>
