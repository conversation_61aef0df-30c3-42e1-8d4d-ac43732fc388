<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:fitsSystemWindows="false">

    <Button
        android:id="@+id/settings_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:layout_marginStart="16dp"
        android:text="⚙️ Ajustes"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        android:padding="16dp"
        android:background="@android:color/transparent"
        android:shadowColor="#000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2"
        android:minWidth="0dp"
        android:minHeight="0dp" />

    <Button
        android:id="@+id/optimize_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="16dp"
        android:text="⚡ Optimizar"
        android:textColor="#FFFFFF"
        android:textSize="12sp"
        android:padding="16dp"
        android:background="@android:color/transparent"
        android:shadowColor="#000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2"
        android:minWidth="0dp"
        android:minHeight="0dp" />

    <GridView
        android:id="@+id/grid_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/settings_button"
        android:padding="8dp"
        android:scrollbars="none"
        android:clipToPadding="false"
        android:numColumns="auto_fit"
        android:columnWidth="80dp"
        android:horizontalSpacing="4dp"
        android:verticalSpacing="4dp"
        android:stretchMode="columnWidth"
        android:listSelector="@android:color/transparent"
        android:drawSelectorOnTop="false"
        android:cacheColorHint="@android:color/transparent"
        android:fadeScrollbars="true"
        android:fastScrollEnabled="false"
        android:smoothScrollbar="false"
        android:scrollingCache="false" />

</RelativeLayout>