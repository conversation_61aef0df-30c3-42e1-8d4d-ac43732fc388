-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:2:1-50:12
	package
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.SET_WALLPAPER_HINTS
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:4:5-78
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:4:22-75
uses-permission#android.permission.KILL_BACKGROUND_PROCESSES
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:5:5-84
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:5:22-81
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:8:5-9:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:9:9-35
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:8:22-74
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:10:5-81
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:10:22-78
queries
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:13:5-18:15
intent#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:14:9-17:18
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:15:13-65
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:15:21-62
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:16:13-73
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:16:23-70
application
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:20:5-48:19
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:20:5-48:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml
	android:label
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:23:9-41
	android:icon
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:22:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:21:9-36
	android:theme
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:24:9-52
activity#com.devfriend.fasterlauncher.MainActivity
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:25:9-45:20
	android:clearTaskOnLaunch
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:31:13-46
	android:stateNotNeeded
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:32:13-42
	android:excludeFromRecents
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:33:13-46
	android:launchMode
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:30:13-44
	android:exported
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:27:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:29:13-74
	android:allowTaskReparenting
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:38:13-49
	android:screenOrientation
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:28:13-52
	android:noHistory
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:36:13-38
	android:alwaysRetainTaskState
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:35:13-49
	android:finishOnTaskLaunch
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:34:13-47
	android:taskAffinity
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:37:13-36
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:26:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.HOME+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:39:13-44:29
	android:priority
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:39:28-48
category#android.intent.category.HOME
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:42:17-73
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:42:27-70
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:43:17-76
	android:name
		ADDED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml:43:27-73
uses-sdk
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\AndroidStudioProjects\GameFaster\app\src\main\AndroidManifest.xml
