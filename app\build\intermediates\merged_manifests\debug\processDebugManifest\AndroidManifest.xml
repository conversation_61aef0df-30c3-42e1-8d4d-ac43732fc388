<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.devfriend.fasterlauncher"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="34" />

    <uses-permission android:name="android.permission.SET_WALLPAPER_HINTS" />
    <uses-permission android:name="android.permission.KILL_BACKGROUND_PROCESSES" />

    <!-- Permisos para Android 14+ compatibilidad -->
    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        android:maxSdkVersion="29" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <!-- Queries para Android 11+ para poder ver todas las aplicaciones -->
    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />

            <category android:name="android.intent.category.LAUNCHER" />
        </intent>
    </queries>

    <application
        android:allowBackup="false"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:testOnly="true"
        android:theme="@style/Theme.FasterLauncher" >
        <activity
            android:name="com.devfriend.fasterlauncher.MainActivity"
            android:allowTaskReparenting="false"
            android:alwaysRetainTaskState="true"
            android:clearTaskOnLaunch="false"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:excludeFromRecents="true"
            android:exported="true"
            android:finishOnTaskLaunch="false"
            android:launchMode="singleTask"
            android:noHistory="false"
            android:screenOrientation="unspecified"
            android:stateNotNeeded="true"
            android:taskAffinity="" >
            <intent-filter android:priority="1" >
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.HOME" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity>
    </application>

</manifest>